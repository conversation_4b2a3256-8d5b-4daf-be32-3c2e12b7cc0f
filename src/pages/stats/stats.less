// life-elephant-app/src/pages/stats/stats.less
.stats-page {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;

  // 统计概览
  .stats-overview {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .overview-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 30rpx;
      text-align: center;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30rpx;
      margin-bottom: 40rpx;

      .stat-item {
        text-align: center;
        padding: 30rpx 20rpx;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 16rpx;
        color: #fff;

        &:nth-child(2) {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &:nth-child(3) {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        &:nth-child(4) {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .stat-value {
          font-size: 48rpx;
          font-weight: bold;
          line-height: 1;
          margin-bottom: 10rpx;
        }

        .stat-label {
          font-size: 24rpx;
          opacity: 0.9;
        }
      }
    }

    .range-stats {
      border-top: 2rpx solid #f0f0f0;
      padding-top: 30rpx;

      .range-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15rpx 0;

        .range-label {
          font-size: 28rpx;
          color: #666;
        }

        .range-value {
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
        }
      }
    }
  }

  // 图表区域
  .chart-section {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .chart-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 30rpx;
      text-align: center;
    }

    .chart-container {
      height: 400rpx;
      margin-bottom: 30rpx;
      border: 2rpx solid #f0f0f0;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fafafa;

      .chart-canvas {
        width: 100%;
        height: 100%;
      }
    }

    .chart-legend {
      display: flex;
      justify-content: center;
      gap: 40rpx;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 10rpx;

        .legend-color {
          width: 20rpx;
          height: 20rpx;
          border-radius: 50%;

          &.count-color {
            background: #4facfe;
          }

          &.change-color {
            background: #fa709a;
          }
        }

        .legend-text {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }

  // 记录列表
  .records-section {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .records-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 30rpx;
      text-align: center;
    }

    .records-list {
      max-height: 600rpx;
      overflow-y: auto;

      .record-item {
        display: flex;
        align-items: center;
        padding: 25rpx 0;
        border-bottom: 2rpx solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .record-left {
          flex: 1;

          .record-date {
            font-size: 28rpx;
            color: #333;
            font-weight: bold;
          }

          .record-time {
            font-size: 24rpx;
            color: #999;
            margin-top: 5rpx;
          }
        }

        .record-center {
          flex: 1;
          text-align: center;

          .record-count {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
          }
        }

        .record-right {
          flex: 1;
          text-align: right;

          .record-change {
            font-size: 28rpx;
            font-weight: bold;
          }
        }
      }
    }

    .empty-records {
      text-align: center;
      padding: 80rpx 0;

      .empty-text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }

  // 数据管理
  .data-management {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .management-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 30rpx;
      text-align: center;
    }

    .management-buttons {
      display: flex;
      gap: 20rpx;

      .management-btn {
        margin-top: 0rpx !important;
        flex: 1;
        height: 80rpx;
        border-radius: 12rpx;
        font-size: 28rpx;
        font-weight: bold;
        border: none;
        color: #fff;

        &.export-btn {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.clear-btn {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        &:active {
          opacity: 0.8;
          transform: scale(0.98);
        }
      }
    }
  }

  // 弹窗样式
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background: #fff;
      border-radius: 20rpx;
      padding: 40rpx;
      margin: 40rpx;
      max-width: 600rpx;
      width: 100%;

      .modal-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        text-align: center;
        margin-bottom: 20rpx;
      }

      .modal-text {
        font-size: 28rpx;
        color: #666;
        text-align: center;
        line-height: 1.5;
        margin-bottom: 40rpx;
      }

      .modal-buttons {
        display: flex;
        gap: 20rpx;

        .modal-btn {
          flex: 1;
          height: 80rpx;
          border-radius: 12rpx;
          font-size: 28rpx;
          font-weight: bold;
          border: none;

          &.cancel-btn {
            background: #f0f0f0;
            color: #666;
          }

          &.confirm-btn {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: #fff;
          }

          &:active {
            opacity: 0.8;
            transform: scale(0.98);
          }
        }
      }
    }
  }

  // 通用样式
  .increase-text {
    color: #4facfe;
  }

  .decrease-text {
    color: #fa709a;
  }
}
