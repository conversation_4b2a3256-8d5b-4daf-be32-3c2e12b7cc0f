// life-elephant-app/src/pages/index/index.less
.headcount-page {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;

  // 当前人数显示区域
  .current-count-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    margin-bottom: 40rpx;
    text-align: center;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);

    .count-display {
      .count-label {
        display: block;
        color: rgba(255, 255, 255, 0.8);
        font-size: 28rpx;
        margin-bottom: 20rpx;
      }

      .count-number {
        display: block;
        color: #fff;
        font-size: 120rpx;
        font-weight: bold;
        line-height: 1;
        margin-bottom: 10rpx;
      }

      .count-unit {
        color: rgba(255, 255, 255, 0.9);
        font-size: 32rpx;
      }
    }
  }

  // 操作区域
  .operation-section {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .operation-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 30rpx;
      text-align: center;
    }

    // 快速操作按钮
    .quick-operations {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 15rpx;
      margin-bottom: 40rpx;

      .operation-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 12rpx;
        font-size: 32rpx;
        font-weight: bold;
        border: none;
        color: #fff;
        margin-top: 0rpx !important;

        &.increase-btn {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.decrease-btn {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        &:active {
          opacity: 0.8;
          transform: scale(0.98);
        }
      }
    }

    // 自定义输入区域
    .custom-input-section,
    .direct-set-section {
      border-top: 2rpx solid #f0f0f0;
      padding-top: 30rpx;
      margin-top: 30rpx;

      .input-group {
        margin-bottom: 20rpx;

        .input-label {
          display: block;
          font-size: 28rpx;
          color: #666;
          margin-bottom: 15rpx;
        }

        .number-input {
          width: 90%;
          height: 80rpx;
          border: 2rpx solid #e0e0e0;
          border-radius: 12rpx;
          padding: 0 20rpx;
          font-size: 30rpx;
          background: #fafafa;

          &:focus {
            border-color: #4facfe;
            background: #fff;
          }
        }
      }

      .custom-buttons {
        display: flex;
        gap: 20rpx;

        .custom-btn {
          flex: 1;
          height: 80rpx;
          border-radius: 12rpx;
          font-size: 28rpx;
          font-weight: bold;
          border: none;
          color: #fff;

          &.increase-btn {
            margin-top: 0rpx !important;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }

          &.decrease-btn {
            margin-top: 0rpx !important;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
          }

          &.set-btn {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
          }

          &:disabled {
            background: #ccc;
            color: #999;
          }

          &:active:not(:disabled) {
            opacity: 0.8;
            transform: scale(0.98);
          }
        }
      }

      .custom-btn {
        width: 100%;
        height: 80rpx;
        border-radius: 12rpx;
        font-size: 28rpx;
        font-weight: bold;
        border: none;
        color: #333;
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

        &:disabled {
          background: #ccc;
          color: #999;
        }

        &:active:not(:disabled) {
          opacity: 0.8;
          transform: scale(0.98);
        }
      }
    }
  }

  // 今日变化和最近记录
  .today-changes,
  .recent-records {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 30rpx;
    }

    .change-info {
      text-align: center;

      .change-text {
        font-size: 28rpx;
        color: #666;
      }
    }

    .records-list {
      .record-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 2rpx solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .record-date {
          font-size: 28rpx;
          color: #666;
        }

        .record-count {
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
        }

        .record-change {
          font-size: 28rpx;
          font-weight: bold;
        }
      }
    }
  }

  // 通用样式
  .increase-text {
    color: #4facfe;
  }

  .decrease-text {
    color: #fa709a;
  }
}
