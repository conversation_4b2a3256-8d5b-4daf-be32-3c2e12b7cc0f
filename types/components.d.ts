// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    Counter: typeof import('./../src/components/Counter.vue')['default']
    Test: typeof import('./../src/components/Test.vue')['default']
    Test2: typeof import('./../src/components/Test2.vue')['default']
  }
}
