{"name": "life-elephant-app", "version": "1.0.0", "private": true, "description": "公司人数统计小程序 - 基于Taro Vue3开发", "templateInfo": {"name": "vue3-pinia-unocss", "typescript": true, "css": "less"}, "license": "MIT", "author": "", "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "dependencies": {"@babel/runtime": "^7.21.0", "@tarojs/components": "3.6.13", "@tarojs/helper": "3.6.13", "@tarojs/plugin-framework-vue3": "3.6.13", "@tarojs/plugin-http": "3.6.13", "@tarojs/plugin-platform-alipay": "3.6.13", "@tarojs/plugin-platform-h5": "3.6.13", "@tarojs/plugin-platform-jd": "3.6.13", "@tarojs/plugin-platform-qq": "3.6.13", "@tarojs/plugin-platform-swan": "3.6.13", "@tarojs/plugin-platform-tt": "3.6.13", "@tarojs/plugin-platform-weapp": "3.6.13", "@tarojs/runtime": "3.6.13", "@tarojs/shared": "3.6.13", "@tarojs/taro": "3.6.13", "axios": "^1.4.0", "pinia": "^2.0.34", "taro-plugin-pinia": "^1.0.0", "taro-plugin-unocss": "^0.0.3", "vue": "^3.2.47"}, "devDependencies": {"@babel/core": "^7.21.4", "@mistjs/eslint-config-vue": "^0.0.2", "@tarojs/cli": "3.6.13", "@tarojs/plugin-html": "^3.6.13", "@tarojs/webpack5-runner": "3.6.13", "@types/webpack-env": "^1.18.0", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vue/babel-plugin-jsx": "^1.1.1", "@vue/compiler-sfc": "^3.2.47", "babel-preset-taro": "3.6.13", "css-loader": "3.4.2", "eslint": "^8.38.0", "eslint-config-taro": "3.6.13", "eslint-plugin-vue": "^8.7.1", "postcss": "^8.4.21", "style-loader": "1.3.0", "stylelint": "9.3.0", "taro-plugin-unocss": "0.0.3", "typescript": "^4.9.5", "unplugin-auto-import": "^0.11.5", "unplugin-vue-components": "^0.22.12", "vue-loader": "^16.8.3", "webpack": "5.78.0"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["vite"]}}}