# 公司人数统计小程序

基于 Taro Vue3 开发的微信小程序，用于统计和记录公司人数的变化。

## 功能特性

### 📊 人数统计页面

- **当前人数显示**：大屏显示当前公司总人数
- **快速操作**：提供 +1、+5、-1、-5 快速操作按钮
- **自定义变更**：支持输入任意数量进行增减
- **直接设置**：可直接设置总人数
- **今日变化**：显示当日人数变化情况
- **最近记录**：展示最近几天的变化记录

### 📈 统计趋势页面

- **统计概览**：显示当前人数、记录天数、总增减等关键指标
- **最值统计**：显示历史最高/最低人数和平均变化
- **趋势图表**：预留图表展示区域（可扩展）
- **详细记录**：完整的历史变化记录列表
- **数据管理**：支持数据导出和清空功能

### 💾 数据存储

- **本地存储**：使用 localStorage 持久化保存数据
- **自动记录**：每次变更自动记录时间和变化量
- **智能合并**：同一天的多次变更会智能合并
- **历史保留**：保留最近 30 天的详细记录

## 技术栈

- **框架**：Taro 3.6.13 + Vue 3
- **状态管理**：Pinia
- **样式**：UnoCSS + Less
- **构建工具**：Webpack 5
- **平台**：微信小程序

## 开发指南

### 环境要求

- Node.js >= 16
- npm >= 8
- 微信开发者工具

### 安装依赖

```bash
npm install
```

### 开发调试

```bash
# 启动微信小程序开发模式
npm run dev:weapp
```

### 构建发布

```bash
# 构建生产版本
npm run build:weapp
```

### 项目结构

```
src/
├── app.config.ts          # 应用配置
├── app.ts                 # 应用入口
├── app.less              # 全局样式
├── stores/               # 状态管理
│   └── headcount.ts      # 人数统计store
├── pages/                # 页面
│   ├── index/            # 人数统计页面
│   │   ├── index.vue
│   │   └── index.less
│   └── stats/            # 统计趋势页面
│       ├── stats.vue
│       └── stats.less
└── assets/               # 静态资源
```

## 使用说明

### 首次使用

1. 打开小程序，当前人数默认为 0
2. 使用快速操作按钮或自定义输入来设置初始人数
3. 每次变更都会自动记录到历史中

### 日常操作

1. **增加人数**：点击 +1、+5 按钮或使用自定义增加
2. **减少人数**：点击 -1、-5 按钮或使用自定义减少
3. **直接设置**：输入目标人数直接设置总数
4. **查看统计**：切换到"统计趋势"页面查看详细数据

### 数据管理

- **导出数据**：在统计页面点击"导出数据"按钮
- **清空记录**：点击"清空数据"按钮（会保留当前人数）
- **自动备份**：数据自动保存在本地，无需手动备份

## 扩展功能

### 可扩展的功能点

1. **图表展示**：集成 F2 或其他图表库显示趋势图
2. **数据同步**：接入云数据库实现多端同步
3. **通知提醒**：设置人数变化通知
4. **权限管理**：添加管理员权限控制
5. **导入导出**：支持 Excel 格式的数据导入导出

### 自定义配置

可以通过修改 `stores/headcount.ts` 中的配置来调整：

- 历史记录保留天数（默认 30 天）
- 数据存储键名
- 统计计算逻辑

## 注意事项

1. **数据安全**：数据存储在本地，卸载小程序会丢失数据
2. **操作确认**：重要操作（如清空数据）会有确认弹窗
3. **数据校验**：输入数据会进行合理性校验，防止异常值
4. **性能优化**：大量历史数据会自动清理，保持应用流畅

## 更新日志

### v1.0.0 (2025-08-07)

- ✨ 初始版本发布
- ✨ 实现基础人数统计功能
- ✨ 添加统计趋势页面
- ✨ 支持数据导出和清空
- ✨ 完善的 UI 设计和交互体验

## 许可证

MIT License
