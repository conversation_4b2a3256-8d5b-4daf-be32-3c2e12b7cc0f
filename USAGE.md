# 使用指南

## 快速开始

### 1. 在微信开发者工具中打开项目

1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目根目录下的 `dist` 文件夹
4. 填写项目信息：
   - 项目名称：公司人数统计
   - AppID：使用测试号或申请的小程序AppID

### 2. 预览和调试

项目编译完成后，可以在微信开发者工具中：
- 点击"预览"生成二维码，用微信扫码在手机上预览
- 点击"真机调试"进行真机调试
- 使用模拟器进行功能测试

## 功能使用说明

### 人数统计页面（首页）

#### 当前人数显示
- 页面顶部显示当前公司总人数
- 采用大字体突出显示，便于快速查看

#### 快速操作
- **+1 按钮**：增加1人
- **+5 按钮**：增加5人  
- **-1 按钮**：减少1人
- **-5 按钮**：减少5人

#### 自定义变更
1. 在"自定义变更数量"输入框中输入数字
2. 点击"增加 X 人"或"减少 X 人"按钮
3. 输入框会自动清空

#### 直接设置总数
1. 在"直接设置总人数"输入框中输入目标人数
2. 点击"设置为 X 人"按钮
3. 系统会自动计算变化量并记录

#### 今日变化
- 显示当天的人数变化情况
- 绿色表示增加，红色表示减少

#### 最近记录
- 显示最近5天的变化记录
- 包含日期、人数和变化量

### 统计趋势页面

#### 统计概览
四个关键指标卡片：
- **当前人数**：实时显示当前总人数
- **记录天数**：已记录数据的天数
- **总增加**：历史累计增加人数
- **总减少**：历史累计减少人数

#### 范围统计
- **最高人数**：历史记录中的最高人数
- **最低人数**：历史记录中的最低人数  
- **平均变化**：每天平均变化量

#### 趋势图表
- 预留图表展示区域
- 可扩展集成图表库显示趋势

#### 详细记录
- 完整的历史变化记录列表
- 按时间倒序排列
- 显示具体的变更时间

#### 数据管理
- **导出数据**：将数据导出为JSON格式
- **清空数据**：清空历史记录（保留当前人数）

## 数据说明

### 数据存储
- 使用浏览器localStorage存储
- 数据键名：
  - `company-headcount`：当前人数
  - `company-headcount-records`：历史记录

### 数据结构
```javascript
// 当前人数
currentCount: number

// 历史记录
records: [
  {
    date: "2025-08-07",      // 日期
    count: 150,              // 当日结束时人数
    change: 5,               // 当日变化量
    timestamp: 1691404800000 // 时间戳
  }
]
```

### 数据特性
- **自动合并**：同一天的多次操作会合并为一条记录
- **智能计算**：自动计算变化量和统计数据
- **历史保留**：保留最近30天记录，超出自动清理
- **实时更新**：每次操作立即保存到本地

## 常见问题

### Q: 数据会丢失吗？
A: 数据存储在本地，只要不清除浏览器数据或卸载小程序，数据就不会丢失。建议定期导出备份。

### Q: 可以修改历史记录吗？
A: 当前版本不支持修改历史记录，只能清空重新开始。

### Q: 支持多人同时使用吗？
A: 当前版本是单机版，每个设备独立存储数据。如需多人协作，需要扩展云端同步功能。

### Q: 人数可以设置为负数吗？
A: 不可以，系统会自动限制最小值为0。

### Q: 如何备份数据？
A: 在统计页面点击"导出数据"，复制JSON数据保存到安全位置。

## 扩展开发

### 添加图表功能
可以集成以下图表库：
- **F2**：蚂蚁金服的移动端图表库
- **uCharts**：跨平台图表库
- **ECharts**：功能强大的图表库

### 添加云端同步
可以接入以下云服务：
- **微信云开发**：官方云服务
- **LeanCloud**：第三方BaaS服务
- **自建API**：自定义后端服务

### 添加通知功能
- 使用小程序订阅消息
- 设置人数变化提醒
- 定时统计报告

## 技术支持

如有问题或建议，请通过以下方式联系：
- 项目Issues：提交GitHub Issues
- 技术交流：加入开发者群组
- 邮件支持：发送邮件至技术支持邮箱

---

*最后更新：2025-08-07*
